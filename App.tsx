import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { dataSource } from './src/database/DataSource';
import AppNavigator from './src/navigation/AppNavigator';
import { ThemeProvider, GradientBackground } from './src/components';

export default function App() {
  const [isLoading, setIsLoading] = React.useState(true);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize database and data source
        await dataSource.init();
        await dataSource.getAndUpdateVisitTime();
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (isLoading) {
    return (
      <ThemeProvider>
        <SafeAreaProvider>
          <GradientBackground variant="candy">
            <View style={styles.loadingContainer}>
              <View style={styles.logoContainer}>
                <Text style={styles.logoEmoji}>🍭</Text>
                <View>
                  <Text style={styles.logoTitle}>Candy Yourself</Text>
                  <Text style={styles.logoSubtitle}>Loading...</Text>
                </View>
              </View>
            </View>
          </GradientBackground>
          <StatusBar style="auto" />
        </SafeAreaProvider>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider>
      <SafeAreaProvider>
        <AppNavigator />
        <StatusBar style="auto" />
      </SafeAreaProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 20,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 50,
  },
  logoEmoji: {
    fontSize: 80,
    marginRight: 20,
  },
  logoTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  logoSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
});

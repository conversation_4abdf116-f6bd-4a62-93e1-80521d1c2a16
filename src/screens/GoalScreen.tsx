import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { dataSource } from '../database/DataSource';
import { getSemanticColors, commonStyles, spacing, textStyles } from '../styles';
import { LongTermGoal, Tag, TagType } from '../types';
import { daysBetween, generateId, validateGoalDates } from '../utils';
import { useTheme } from '../components';

const GoalScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const colors = getSemanticColors(theme);
  const styles = createStyles(colors);
  const [goals, setGoals] = useState<LongTermGoal[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isCreating, setIsCreating] = useState<boolean>(false);

  // Form state
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [targetNumber, setTargetNumber] = useState<string>('');
  const [rewardPoints, setRewardPoints] = useState<string>('');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)); // 7 days from now

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setGoals(dataSource.getGoals());
    setTags(dataSource.getTagsByType(TagType.PAIN));
  };

  const handleCreateGoal = async () => {
    if (!selectedTag) {
      Alert.alert('Error', 'Please select a task category');
      return;
    }

    if (!targetNumber.trim() || isNaN(Number(targetNumber)) || Number(targetNumber) <= 0) {
      Alert.alert('Error', 'Please enter a valid target number');
      return;
    }

    if (!rewardPoints.trim() || isNaN(Number(rewardPoints)) || Number(rewardPoints) <= 0) {
      Alert.alert('Error', 'Please enter valid reward points');
      return;
    }

    const dateError = validateGoalDates(startDate, endDate);
    if (dateError) {
      Alert.alert('Error', dateError);
      return;
    }

    const newGoal: LongTermGoal = {
      id: generateId(),
      tagId: selectedTag.id,
      targetNo: Number(targetNumber),
      currentNo: 0,
      point: Number(rewardPoints),
      startTime: startDate,
      endTime: endDate,
      status: 'inprogress',
      createdAt: new Date(),
    };

    try {
      await dataSource.addGoal(newGoal);
      setGoals(dataSource.getGoals());
      resetForm();
      setIsCreating(false);
      Alert.alert('Success', 'Goal created successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to create goal');
    }
  };

  const handleDeleteGoal = async (goal: LongTermGoal) => {
    Alert.alert(
      'Delete Goal',
      'Are you sure you want to delete this goal?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await dataSource.deleteGoal(goal);
              setGoals(dataSource.getGoals());
            } catch (error) {
              Alert.alert('Error', 'Failed to delete goal');
            }
          },
        },
      ]
    );
  };

  const resetForm = () => {
    setSelectedTag(null);
    setTargetNumber('');
    setRewardPoints('');
    setStartDate(new Date());
    setEndDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
  };

  const renderGoalItem = (goal: LongTermGoal) => {
    const progress = goal.targetNo > 0 ? (goal.currentNo / goal.targetNo) * 100 : 0;
    const isCompleted = goal.status === 'done';
    const daysRemaining = daysBetween(new Date(), goal.endTime);

    return (
      <View key={goal.id} style={[styles.goalItem, isCompleted && styles.completedGoal]}>
        <View style={styles.goalHeader}>
          <Text style={styles.goalTitle}>
            {goal.targetNo} x {dataSource.getTagNameById(goal.tagId)}
          </Text>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteGoal(goal)}
          >
            <Ionicons name="trash-outline" size={20} color={colors.error} />
          </TouchableOpacity>
        </View>

        <Text style={styles.goalReward}>Reward: +{goal.point} points</Text>

        <View style={styles.goalDates}>
          <Text style={styles.goalDate}>
            {goal.startTime.toLocaleDateString()} - {goal.endTime.toLocaleDateString()}
          </Text>
          {!isCompleted && (
            <Text style={[styles.daysRemaining, daysRemaining < 0 && styles.overdue]}>
              {daysRemaining < 0 ? 'Overdue' : `${daysRemaining} days left`}
            </Text>
          )}
        </View>

        <View style={styles.progressContainer}>
          <View style={commonStyles.progressBar}>
            <View
              style={[
                commonStyles.progressFill,
                { width: `${Math.min(progress, 100)}%` },
                isCompleted && { backgroundColor: colors.success }
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {goal.currentNo} / {goal.targetNo} {isCompleted && '✅'}
          </Text>
        </View>

        {isCompleted && (
          <Text style={styles.completedText}>Goal Completed! 🎉</Text>
        )}
      </View>
    );
  };

  const renderTagSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Select Task Category</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.tagsContainer}>
          {tags.map((tag) => (
            <TouchableOpacity
              key={tag.id}
              style={[
                styles.tagItem,
                selectedTag?.id === tag.id && styles.selectedTagItem
              ]}
              onPress={() => setSelectedTag(tag)}
            >
              <Text style={[
                styles.tagText,
                selectedTag?.id === tag.id && styles.selectedTagText
              ]}>
                {tag.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  if (isCreating) {
    return (
      <KeyboardAvoidingView
        style={commonStyles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => {
                setIsCreating(false);
                resetForm();
              }}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.title}>Create New Goal</Text>
          </View>

          {renderTagSelector()}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Target Number</Text>
            <TextInput
              style={commonStyles.input}
              placeholder="How many times?"
              value={targetNumber}
              onChangeText={setTargetNumber}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Reward Points</Text>
            <TextInput
              style={commonStyles.input}
              placeholder="Points to earn when completed"
              value={rewardPoints}
              onChangeText={setRewardPoints}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Duration</Text>
            <Text style={styles.dateInfo}>
              From: {startDate.toLocaleDateString()}
            </Text>
            <Text style={styles.dateInfo}>
              To: {endDate.toLocaleDateString()}
            </Text>
            <Text style={styles.dateInfo}>
              Duration: {daysBetween(startDate, endDate)} days
            </Text>
          </View>

          <TouchableOpacity
            style={[commonStyles.button, commonStyles.buttonSuccess]}
            onPress={handleCreateGoal}
          >
            <Text style={commonStyles.buttonText}>Create Goal</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }

  return (
    <View style={commonStyles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Goals</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            if (tags.length === 0) {
              Alert.alert(
                'No Tasks Available',
                'Create some tasks first before setting goals',
                [{ text: 'OK' }]
              );
              return;
            }
            setIsCreating(true);
          }}
        >
          <Ionicons name="add" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {goals.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="flag-outline" size={64} color={colors.textTertiary} />
            <Text style={styles.emptyTitle}>No Goals Yet</Text>
            <Text style={styles.emptySubtitle}>
              Set goals to stay motivated and earn bonus points!
            </Text>
            <TouchableOpacity
              style={[commonStyles.button, commonStyles.buttonPrimary]}
              onPress={() => {
                if (tags.length === 0) {
                  Alert.alert(
                    'No Tasks Available',
                    'Create some tasks first before setting goals',
                    [{ text: 'OK' }]
                  );
                  return;
                }
                setIsCreating(true);
              }}
            >
              <Text style={commonStyles.buttonText}>Create Your First Goal</Text>
            </TouchableOpacity>
          </View>
        ) : (
          goals.map(renderGoalItem)
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.layout.screenPadding,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.layout.screenPadding,
    paddingTop: spacing.component.safeAreaTop,
    paddingBottom: spacing.md,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  backButton: {
    padding: spacing.sm,
  },
  title: {
    ...textStyles.h3,
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    padding: spacing.sm,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...textStyles.h5,
    color: colors.text,
    marginBottom: spacing.md,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  tagItem: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: spacing.component.radiusMD,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
  },
  selectedTagItem: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
  },
  tagText: {
    ...textStyles.body,
    color: colors.text,
  },
  selectedTagText: {
    color: colors.textLight,
  },
  dateInfo: {
    ...textStyles.body,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  goalItem: {
    backgroundColor: colors.background,
    borderRadius: spacing.component.radiusLG,
    padding: spacing.lg,
    marginBottom: spacing.md,
    ...commonStyles.shadowMedium,
  },
  completedGoal: {
    backgroundColor: colors.success + '10',
    borderColor: colors.success,
    borderWidth: 1,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  goalTitle: {
    ...textStyles.h5,
    color: colors.text,
    flex: 1,
  },
  deleteButton: {
    padding: spacing.xs,
  },
  goalReward: {
    ...textStyles.body,
    color: colors.success,
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  goalDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  goalDate: {
    ...textStyles.bodySmall,
    color: colors.textSecondary,
  },
  daysRemaining: {
    ...textStyles.bodySmall,
    color: colors.info,
    fontWeight: 'bold',
  },
  overdue: {
    color: colors.error,
  },
  progressContainer: {
    marginTop: spacing.sm,
  },
  progressText: {
    ...textStyles.footnote,
    color: colors.textSecondary,
    textAlign: 'right',
    marginTop: spacing.xs,
  },
  completedText: {
    ...textStyles.body,
    color: colors.success,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing['3xl'],
  },
  emptyTitle: {
    ...textStyles.h3,
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    ...textStyles.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
});

export default GoalScreen;

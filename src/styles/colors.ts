// Modern color palette with enhanced design tokens
export const lightColors = {
  // Primary brand colors - Modern candy-inspired palette
  primary: {
    50: '#FFF0F5',
    100: '#FFE1EC',
    200: '#FFC7D9',
    300: '#FF9DBF',
    400: '#FF6B9D',
    500: '#FF4081', // Main primary
    600: '#E91E63',
    700: '#C2185B',
    800: '#AD1457',
    900: '#880E4F',
  },

  secondary: {
    50: '#E0F7F5',
    100: '#B3EDEA',
    200: '#80E1DC',
    300: '#4DD5CE',
    400: '#26CCC4',
    500: '#00BCD4', // Main secondary
    600: '#00ACC1',
    700: '#0097A7',
    800: '#00838F',
    900: '#006064',
  },

  accent: {
    50: '#FFFEF7',
    100: '#FFFDE7',
    200: '#FFF9C4',
    300: '#FFF59D',
    400: '#FFF176',
    500: '#FFEB3B', // Main accent
    600: '#FDD835',
    700: '#FBC02D',
    800: '#F9A825',
    900: '#F57F17',
  },

  // Neutral colors - Modern gray scale
  neutral: {
    0: '#FFFFFF',
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
    950: '#0A0A0A',
  },

  // Semantic colors
  success: {
    50: '#E8F5E8',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50', // Main success
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
  },

  warning: {
    50: '#FFF8E1',
    100: '#FFECB3',
    200: '#FFE082',
    300: '#FFD54F',
    400: '#FFCA28',
    500: '#FFC107', // Main warning
    600: '#FFB300',
    700: '#FFA000',
    800: '#FF8F00',
    900: '#FF6F00',
  },

  error: {
    50: '#FFEBEE',
    100: '#FFCDD2',
    200: '#EF9A9A',
    300: '#E57373',
    400: '#EF5350',
    500: '#F44336', // Main error
    600: '#E53935',
    700: '#D32F2F',
    800: '#C62828',
    900: '#B71C1C',
  },

  info: {
    50: '#E3F2FD',
    100: '#BBDEFB',
    200: '#90CAF9',
    300: '#64B5F6',
    400: '#42A5F5',
    500: '#2196F3', // Main info
    600: '#1E88E5',
    700: '#1976D2',
    800: '#1565C0',
    900: '#0D47A1',
  },

  // Activity type colors - Enhanced with better contrast
  pain: {
    background: '#FFEBEE',
    backgroundHover: '#FFCDD2',
    border: '#F44336',
    borderHover: '#E53935',
    text: '#C62828',
    textSecondary: '#D32F2F',
  },

  gain: {
    background: '#E8F5E8',
    backgroundHover: '#C8E6C9',
    border: '#4CAF50',
    borderHover: '#43A047',
    text: '#2E7D32',
    textSecondary: '#388E3C',
  },
} as const;

// Dark theme colors
export const darkColors = {
  // Primary brand colors - Adjusted for dark theme
  primary: {
    50: '#880E4F',
    100: '#AD1457',
    200: '#C2185B',
    300: '#E91E63',
    400: '#FF4081',
    500: '#FF6B9D', // Main primary (brighter for dark)
    600: '#FF9DBF',
    700: '#FFC7D9',
    800: '#FFE1EC',
    900: '#FFF0F5',
  },

  secondary: {
    50: '#006064',
    100: '#00838F',
    200: '#0097A7',
    300: '#00ACC1',
    400: '#00BCD4',
    500: '#4ECDC4', // Main secondary (brighter for dark)
    600: '#80E1DC',
    700: '#B3EDEA',
    800: '#E0F7F5',
    900: '#F0FDFC',
  },

  accent: {
    50: '#F57F17',
    100: '#F9A825',
    200: '#FBC02D',
    300: '#FDD835',
    400: '#FFEB3B',
    500: '#FFF176', // Main accent (brighter for dark)
    600: '#FFF59D',
    700: '#FFF9C4',
    800: '#FFFDE7',
    900: '#FFFEF7',
  },

  // Neutral colors - Dark theme
  neutral: {
    0: '#000000',
    50: '#0A0A0A',
    100: '#121212',
    200: '#1E1E1E',
    300: '#2D2D2D',
    400: '#3D3D3D',
    500: '#4D4D4D',
    600: '#6D6D6D',
    700: '#8D8D8D',
    800: '#ADADAD',
    900: '#E0E0E0',
    950: '#FFFFFF',
  },

  // Semantic colors - Dark theme variants
  success: {
    50: '#1B5E20',
    100: '#2E7D32',
    200: '#388E3C',
    300: '#43A047',
    400: '#4CAF50',
    500: '#66BB6A', // Main success (brighter for dark)
    600: '#81C784',
    700: '#A5D6A7',
    800: '#C8E6C9',
    900: '#E8F5E8',
  },

  warning: {
    50: '#FF6F00',
    100: '#FF8F00',
    200: '#FFA000',
    300: '#FFB300',
    400: '#FFC107',
    500: '#FFCA28', // Main warning (brighter for dark)
    600: '#FFD54F',
    700: '#FFE082',
    800: '#FFECB3',
    900: '#FFF8E1',
  },

  error: {
    50: '#B71C1C',
    100: '#C62828',
    200: '#D32F2F',
    300: '#E53935',
    400: '#F44336',
    500: '#EF5350', // Main error (brighter for dark)
    600: '#E57373',
    700: '#EF9A9A',
    800: '#FFCDD2',
    900: '#FFEBEE',
  },

  info: {
    50: '#0D47A1',
    100: '#1565C0',
    200: '#1976D2',
    300: '#1E88E5',
    400: '#2196F3',
    500: '#42A5F5', // Main info (brighter for dark)
    600: '#64B5F6',
    700: '#90CAF9',
    800: '#BBDEFB',
    900: '#E3F2FD',
  },

  // Activity type colors - Dark theme
  pain: {
    background: '#2D1B1B',
    backgroundHover: '#3D2525',
    border: '#EF5350',
    borderHover: '#E57373',
    text: '#EF9A9A',
    textSecondary: '#E57373',
  },

  gain: {
    background: '#1B2D1B',
    backgroundHover: '#253D25',
    border: '#66BB6A',
    borderHover: '#81C784',
    text: '#A5D6A7',
    textSecondary: '#81C784',
  },
} as const;

// Theme type
export type Theme = 'light' | 'dark';

// Current theme colors (initialized with light theme)
export let colors: any = {
  ...lightColors,
  // Background colors
  background: lightColors.neutral[0],
  backgroundSecondary: lightColors.neutral[50],
  backgroundTertiary: lightColors.neutral[100],
  surface: lightColors.neutral[0],
  surfaceVariant: lightColors.neutral[100],

  // Text colors
  text: lightColors.neutral[900],
  textSecondary: lightColors.neutral[600],
  textTertiary: lightColors.neutral[500],
  textLight: lightColors.neutral[0],
  textOnPrimary: lightColors.neutral[0],
  textOnSecondary: lightColors.neutral[0],

  // Primary colors
  primary: lightColors.primary[500],
  primaryVariant: lightColors.primary[600],
  onPrimary: lightColors.neutral[0],

  // Secondary colors
  secondary: lightColors.secondary[500],
  secondaryVariant: lightColors.secondary[600],
  onSecondary: lightColors.neutral[0],

  // Accent colors
  accent: lightColors.accent[500],
  accentVariant: lightColors.accent[600],
  onAccent: lightColors.neutral[900],

  // Status colors
  success: lightColors.success[500],
  warning: lightColors.warning[500],
  error: lightColors.error[500],
  info: lightColors.info[500],

  // Border colors
  border: lightColors.neutral[300],
  borderLight: lightColors.neutral[200],
  borderDark: lightColors.neutral[400],

  // Shadow colors
  shadow: lightColors.neutral[900],

  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',

  // Button colors
  buttonPrimary: lightColors.primary[500],
  buttonSecondary: lightColors.neutral[200],
  buttonSuccess: lightColors.success[500],
  buttonDanger: lightColors.error[500],

  // Activity colors
  pain: lightColors.pain,
  gain: lightColors.gain,

  // Calendar colors
  calendarToday: lightColors.primary[500],
  calendarSelected: lightColors.secondary[500],
  calendarDisabled: lightColors.neutral[300],

  // Progress colors
  progressBackground: lightColors.neutral[200],
  progressFill: lightColors.primary[500],

  // Gradient colors
  gradientStart: lightColors.primary[500],
  gradientEnd: lightColors.secondary[500],
};

// Semantic color mappings for easy access (defined first to avoid hoisting issues)
export const getSemanticColors = (theme: Theme = 'light') => {
  const themeColors = theme === 'light' ? lightColors : darkColors;

  return {
    // Background colors
    background: themeColors.neutral[0],
    backgroundSecondary: themeColors.neutral[50],
    backgroundTertiary: themeColors.neutral[100],
    surface: themeColors.neutral[0],
    surfaceVariant: themeColors.neutral[100],

    // Text colors
    text: themeColors.neutral[900],
    textSecondary: themeColors.neutral[600],
    textTertiary: themeColors.neutral[500],
    textLight: themeColors.neutral[0],
    textOnPrimary: themeColors.neutral[0],
    textOnSecondary: themeColors.neutral[0],

    // Primary colors
    primary: themeColors.primary[500],
    primaryVariant: themeColors.primary[600],
    onPrimary: themeColors.neutral[0],

    // Secondary colors
    secondary: themeColors.secondary[500],
    secondaryVariant: themeColors.secondary[600],
    onSecondary: themeColors.neutral[0],

    // Accent colors
    accent: themeColors.accent[500],
    accentVariant: themeColors.accent[600],
    onAccent: themeColors.neutral[900],

    // Status colors
    success: themeColors.success[500],
    warning: themeColors.warning[500],
    error: themeColors.error[500],
    info: themeColors.info[500],

    // Border colors
    border: themeColors.neutral[300],
    borderLight: themeColors.neutral[200],
    borderDark: themeColors.neutral[400],

    // Shadow colors
    shadow: themeColors.neutral[900],

    // Overlay colors
    overlay: `rgba(${theme === 'light' ? '0, 0, 0' : '255, 255, 255'}, 0.5)`,
    overlayLight: `rgba(${theme === 'light' ? '0, 0, 0' : '255, 255, 255'}, 0.3)`,

    // Button colors
    buttonPrimary: themeColors.primary[500],
    buttonSecondary: themeColors.neutral[200],
    buttonSuccess: themeColors.success[500],
    buttonDanger: themeColors.error[500],

    // Activity colors
    pain: themeColors.pain,
    gain: themeColors.gain,

    // Calendar colors
    calendarToday: themeColors.primary[500],
    calendarSelected: themeColors.secondary[500],
    calendarDisabled: themeColors.neutral[300],

    // Progress colors
    progressBackground: themeColors.neutral[200],
    progressFill: themeColors.primary[500],

    // Gradient colors
    gradientStart: themeColors.primary[500],
    gradientEnd: themeColors.secondary[500],
  };
};

// Theme switching function
export const setTheme = (theme: Theme) => {
  const themeColors = theme === 'light' ? lightColors : darkColors;
  const semanticColors = getSemanticColors(theme);

  colors = {
    ...themeColors,
    ...semanticColors,
  };
};

// Initialize colors immediately to prevent undefined errors
const initializeColors = () => {
  const themeColors = lightColors;
  const semanticColors = getSemanticColors('light');

  colors = {
    ...themeColors,
    ...semanticColors,
  };
};

// Call initialization immediately
initializeColors();





// Color utility functions
export const getActivityColor = (tagType: 'pain' | 'gain', theme: Theme = 'light') => {
  const themeColors = theme === 'light' ? lightColors : darkColors;
  return tagType === 'pain' ? themeColors.pain : themeColors.gain;
};

export const getStatusColor = (status: 'inprogress' | 'done' | 'cancelled', theme: Theme = 'light') => {
  const semanticColors = getSemanticColors(theme);
  switch (status) {
    case 'inprogress':
      return semanticColors.info;
    case 'done':
      return semanticColors.success;
    case 'cancelled':
      return semanticColors.error;
    default:
      return semanticColors.textSecondary;
  }
};

export const hexToRgba = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Color manipulation utilities
export const lighten = (color: string, amount: number): string => {
  // Simple lighten function - in a real app, you might want to use a library like chroma-js
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * amount);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
};

export const darken = (color: string, amount: number): string => {
  return lighten(color, -amount);
};

// Accessibility helpers
export const getContrastRatio = (color1: string, color2: string): number => {
  // Simplified contrast ratio calculation
  // In a real app, you'd want a more robust implementation
  const getLuminance = (color: string) => {
    const rgb = parseInt(color.replace('#', ''), 16);
    const r = (rgb >> 16) & 0xff;
    const g = (rgb >> 8) & 0xff;
    const b = (rgb >> 0) & 0xff;
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

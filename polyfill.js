// Polyfill for ReadableStream in older Node.js versions
try {
  if (typeof global.ReadableStream === 'undefined') {
    const { ReadableStream } = require('stream/web');
    global.ReadableStream = ReadableStream;
  }

  if (typeof global.WritableStream === 'undefined') {
    const { WritableStream } = require('stream/web');
    global.WritableStream = WritableStream;
  }

  if (typeof global.TransformStream === 'undefined') {
    const { TransformStream } = require('stream/web');
    global.TransformStream = TransformStream;
  }
} catch (error) {
  // Fallback for very old Node.js versions
  console.warn('Web Streams API not available, some features may not work properly');
}

// Polyfill for os.availableParallelism (added in Node.js v18.14.0)
const os = require('os');
if (typeof os.availableParallelism !== 'function') {
  os.availableParallelism = () => {
    return os.cpus().length;
  };
}

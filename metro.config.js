const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add polyfills for Node.js modules
config.resolver.alias = {
  ...config.resolver.alias,
  'stream': 'stream-browserify',
  'http': 'stream-http',
  'https': 'https-browserify',
  'zlib': 'browserify-zlib',
  'util': 'util',
  'buffer': 'buffer',
  'process': 'process/browser',
};

// Add node_modules to the resolver
config.resolver.platforms = ['native', 'web', 'default'];

// Configure transformer to handle polyfills
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

module.exports = config;

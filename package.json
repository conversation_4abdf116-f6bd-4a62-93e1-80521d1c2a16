{"name": "candyyourselfv2", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "expo": "~53.0.15", "expo-sqlite": "^15.2.13", "expo-status-bar": "~2.2.3", "expo-vector-icons": "^10.0.1", "https-browserify": "^1.0.0", "process": "^0.11.10", "react": "19.0.0", "react-native": "0.79.4", "react-native-calendars": "^1.1313.0", "react-native-gesture-handler": "^2.27.1", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "util": "^0.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "node-fetch": "^2.7.0", "typescript": "~5.8.3"}, "private": true}